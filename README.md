# 轮胎磨损状态识别与胎压异常联动预警平台

## 概述

这是一个基于MATLAB App Designer开发的专业轮胎监测与预警系统，集成了轮胎磨损识别、胎压监测、联动预警和数据分析等核心功能。该平台采用现代化的用户界面设计，提供科学的参数输入和高级的数据可视化功能。

## 系统特性

### 🔧 技术特性
- **兼容性**: 支持MATLAB R2023b及更高版本
- **布局管理**: 使用Grid Layout和Fixed Position进行响应式布局
- **现代UI**: 采用现代化配色方案和交互元素
- **自适应**: 界面在不同屏幕尺寸下自动调整

### 📊 功能模块

#### 1. 磨损识别模块
- **磨损深度分析**: 输入范围0-10mm，精确评估轮胎磨损程度
- **磨损面积计算**: 百分比显示磨损覆盖面积
- **磨损模式识别**: 支持均匀磨损、偏磨、中心磨损、边缘磨损、斑点磨损等模式
- **可视化**: 使用极坐标图展示轮胎磨损分布

#### 2. 胎压监测模块
- **胎压监测**: 实时监测胎压值(0-5 bar)
- **温度监控**: 环境温度监测(-20°C到80°C)
- **载荷分析**: 车辆载荷监测(0-2000kg)
- **3D可视化**: 胎压-温度关系的三维表面图

#### 3. 联动预警模块
- **预警阈值设置**: 可调节的预警敏感度(0-1)
- **风险指数评估**: 综合风险评分(0-10)
- **寿命预测**: 基于当前状态预测轮胎剩余寿命
- **热力图显示**: 风险评估结果的热力图可视化

#### 4. 数据分析模块
- **多数据源**: 支持实时数据、历史数据、模拟数据
- **分析类型**: 趋势分析、相关性分析、预测分析、异常检测
- **网络图**: 系统关系的网络图可视化
- **综合报告**: 自动生成详细的分析报告

## 安装与使用

### 系统要求
- MATLAB R2023b或更高版本
- App Designer工具箱
- 足够的系统内存(推荐8GB以上)

### 安装步骤
1. 将所有文件下载到MATLAB工作目录
2. 确保以下文件在同一文件夹中：
   - `TireWearPressureWarningPlatform.m`
   - `runTireWarningApp.m`
   - `README.md`

### 启动方法

#### 方法1: 使用启动脚本
```matlab
runTireWarningApp()
```

#### 方法2: 直接创建应用实例
```matlab
app = TireWearPressureWarningPlatform;
```

## 使用指南

### 基本操作流程

1. **启动应用**: 运行启动脚本或直接创建应用实例
2. **选择功能**: 点击左侧导航栏的功能按钮
3. **输入参数**: 在相应页面输入科学参数
4. **执行分析**: 点击分析按钮获取结果
5. **查看结果**: 在可视化区域查看分析结果

### 参数说明

#### 磨损识别参数
- **磨损深度**: 轮胎花纹沟槽的磨损深度，单位毫米
- **磨损面积**: 磨损区域占轮胎总面积的百分比
- **磨损模式**: 轮胎的磨损类型和分布模式

#### 胎压监测参数
- **胎压**: 轮胎内部气压，单位bar
- **温度**: 轮胎或环境温度，单位摄氏度
- **载荷**: 车辆当前载重，单位千克

#### 预警系统参数
- **预警阈值**: 触发预警的敏感度设置
- **风险指数**: 综合风险评估的数值指标

## 技术架构

### 类结构
```
TireWearPressureWarningPlatform (主类)
├── 属性 (Properties)
│   ├── UI组件属性
│   └── 数据存储属性
├── 回调方法 (Callback Methods)
│   ├── 导航回调
│   ├── 分析回调
│   └── 数据处理回调
└── 组件创建方法 (Component Creation)
    ├── 主界面创建
    └── 各页面创建
```

### 数据流
1. 用户输入 → 参数验证 → 数据处理
2. 算法分析 → 结果生成 → 可视化显示
3. 状态更新 → 用户反馈 → 报告生成

## 可视化特性

### 高级图表类型
- **极坐标图**: 轮胎磨损分布的圆形可视化
- **三维表面图**: 胎压-温度关系的立体展示
- **热力图**: 风险评估的颜色编码显示
- **网络图**: 系统组件关系的图形化表示

### 配色方案
- **主色调**: 深蓝色导航栏 (RGB: 51, 77, 102)
- **强调色**: 多种功能色彩区分不同模块
- **背景色**: 浅灰色背景 (RGB: 242, 242, 247)
- **文本色**: 高对比度的黑白文本

## 故障排除

### 常见问题

1. **启动失败**
   - 检查MATLAB版本是否为R2023b或更高
   - 确认App Designer工具箱已安装
   - 验证文件路径是否正确

2. **界面显示异常**
   - 调整屏幕分辨率
   - 重启MATLAB
   - 检查系统内存使用情况

3. **功能无响应**
   - 检查输入参数是否在有效范围内
   - 查看MATLAB命令窗口的错误信息
   - 重新启动应用程序

## 扩展开发

### 添加新功能模块
1. 在主类中添加新的UI组件属性
2. 创建对应的页面创建方法
3. 实现相关的回调函数
4. 更新导航系统

### 自定义可视化
1. 修改现有的绘图函数
2. 添加新的图表类型
3. 调整配色方案
4. 优化布局参数

## 版本信息

- **当前版本**: 1.0.0
- **开发环境**: MATLAB R2023b
- **最后更新**: 2025年7月31日

## 许可证

本项目仅供学习和研究使用。

---

*如有技术问题或改进建议，请联系开发团队。*
