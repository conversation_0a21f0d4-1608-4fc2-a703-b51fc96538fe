classdef TireWearPressureWarningPlatform < matlab.apps.AppBase

    % Properties that correspond to app components
    properties (Access = public)
        UIFigure                        matlab.ui.Figure
        GridLayout                      matlab.ui.container.GridLayout
        
        % Navigation Panel
        NavigationPanel                 matlab.ui.container.Panel
        NavigationGrid                  matlab.ui.container.GridLayout
        TitleLabel                      matlab.ui.control.Label
        Tab1Button                      matlab.ui.control.Button
        Tab2Button                      matlab.ui.control.Button
        Tab3Button                      matlab.ui.control.Button
        Tab4Button                      matlab.ui.control.Button
        
        % Main Content Panel
        MainPanel                       matlab.ui.container.Panel
        MainGrid                        matlab.ui.container.GridLayout
        
        % Page 1: 轮胎磨损识别
        Page1Panel                      matlab.ui.container.Panel
        Page1Grid                       matlab.ui.container.GridLayout
        WearDepthLabel                  matlab.ui.control.Label
        WearDepthField                  matlab.ui.control.NumericEditField
        WearAreaLabel                   matlab.ui.control.Label
        WearAreaField                   matlab.ui.control.NumericEditField
        WearPatternLabel                matlab.ui.control.Label
        WearPatternDropDown             matlab.ui.control.DropDown
        AnalyzeWearButton               matlab.ui.control.Button
        WearResultAxes                  matlab.ui.control.UIAxes
        WearStatusLabel                 matlab.ui.control.Label
        
        % Page 2: 胎压监测
        Page2Panel                      matlab.ui.container.Panel
        Page2Grid                       matlab.ui.container.GridLayout
        PressureLabel                   matlab.ui.control.Label
        PressureField                   matlab.ui.control.NumericEditField
        TemperatureLabel                matlab.ui.control.Label
        TemperatureField                matlab.ui.control.NumericEditField
        LoadLabel                       matlab.ui.control.Label
        LoadField                       matlab.ui.control.NumericEditField
        MonitorButton                   matlab.ui.control.Button
        PressureAxes                    matlab.ui.control.UIAxes
        PressureStatusLabel             matlab.ui.control.Label
        
        % Page 3: 联动预警
        Page3Panel                      matlab.ui.container.Panel
        Page3Grid                       matlab.ui.container.GridLayout
        ThresholdLabel                  matlab.ui.control.Label
        ThresholdField                  matlab.ui.control.NumericEditField
        RiskIndexLabel                  matlab.ui.control.Label
        RiskIndexField                  matlab.ui.control.NumericEditField
        PredictLifeButton               matlab.ui.control.Button
        WarningAxes                     matlab.ui.control.UIAxes
        WarningStatusLabel              matlab.ui.control.Label
        
        % Page 4: 数据分析
        Page4Panel                      matlab.ui.container.Panel
        Page4Grid                       matlab.ui.container.GridLayout
        DataSourceLabel                 matlab.ui.control.Label
        DataSourceDropDown              matlab.ui.control.DropDown
        AnalysisTypeLabel               matlab.ui.control.Label
        AnalysisTypeDropDown            matlab.ui.control.DropDown
        GenerateReportButton            matlab.ui.control.Button
        AnalysisAxes                    matlab.ui.control.UIAxes
        ReportTextArea                  matlab.ui.control.TextArea
        
        % Status Bar
        StatusPanel                     matlab.ui.container.Panel
        StatusLabel                     matlab.ui.control.Label
    end
    
    properties (Access = private)
        CurrentPage = 1  % Current active page
        WearData = []    % Tire wear data
        PressureData = [] % Pressure monitoring data
        WarningData = []  % Warning system data
    end

    % Callbacks that handle component events
    methods (Access = private)

        % Code that executes after component creation
        function startupFcn(app)
            % Initialize the app
            app.UIFigure.Name = '轮胎磨损状态识别与胎压异常联动预警平台';
            app.StatusLabel.Text = '系统已启动，请选择功能模块';
            
            % Set modern color scheme
            app.UIFigure.Color = [0.95 0.95 0.97];
            app.NavigationPanel.BackgroundColor = [0.2 0.3 0.4];
            app.MainPanel.BackgroundColor = [1 1 1];
            
            % Initialize with first page
            showPage(app, 1);
        end

        % Button pushed function: Tab1Button
        function Tab1ButtonPushed(app, event)
            showPage(app, 1);
        end

        % Button pushed function: Tab2Button
        function Tab2ButtonPushed(app, event)
            showPage(app, 2);
        end

        % Button pushed function: Tab3Button
        function Tab3ButtonPushed(app, event)
            showPage(app, 3);
        end

        % Button pushed function: Tab4Button
        function Tab4ButtonPushed(app, event)
            showPage(app, 4);
        end

        % Button pushed function: AnalyzeWearButton
        function AnalyzeWearButtonPushed(app, event)
            % Simulate tire wear analysis with advanced visualization
            wearDepth = app.WearDepthField.Value;
            wearArea = app.WearAreaField.Value;
            pattern = app.WearPatternDropDown.Value;

            % Clear previous plots
            cla(app.WearResultAxes);

            % Create 3D tire simulation visualization
            % Generate tire geometry
            theta = linspace(0, 2*pi, 100);
            r_outer = 1.0;
            r_inner = 0.6;

            % Apply wear pattern to outer radius
            switch pattern
                case '均匀磨损'
                    wear_profile = r_outer - (wearDepth/10) * (0.1 + 0.05*sin(8*theta));
                case '偏磨'
                    wear_profile = r_outer - (wearDepth/10) * (0.05 + 0.15*max(0, sin(theta + pi/4)));
                case '中心磨损'
                    wear_profile = r_outer - (wearDepth/10) * (0.15 + 0.1*cos(4*theta).^2);
                case '边缘磨损'
                    wear_profile = r_outer - (wearDepth/10) * (0.1 + 0.15*sin(2*theta).^2);
                case '斑点磨损'
                    wear_profile = r_outer - (wearDepth/10) * (0.05 + 0.2*abs(sin(6*theta)).*abs(cos(4*theta)));
            end

            % Create 3D tire visualization
            z_levels = linspace(-0.2, 0.2, 20);
            [THETA, Z] = meshgrid(theta, z_levels);

            % Outer surface with wear
            R_outer = repmat(wear_profile, length(z_levels), 1);
            X_outer = R_outer .* cos(THETA);
            Y_outer = R_outer .* sin(THETA);

            % Inner surface
            R_inner = r_inner * ones(size(THETA));
            X_inner = R_inner .* cos(THETA);
            Y_inner = R_inner .* sin(THETA);

            % Plot 3D tire with wear visualization
            surf(app.WearResultAxes, X_outer, Y_outer, Z, 'FaceColor', 'interp', 'EdgeColor', 'none', 'FaceAlpha', 0.8);
            hold(app.WearResultAxes, 'on');
            surf(app.WearResultAxes, X_inner, Y_inner, Z, 'FaceColor', [0.3 0.3 0.3], 'EdgeColor', 'none', 'FaceAlpha', 0.6);

            % Add wear depth color mapping
            wear_intensity = (r_outer - wear_profile) / (wearDepth/10);
            colormap(app.WearResultAxes, 'hot');

            % Add tire tread pattern simulation
            for i = 1:8
                angle_pos = i * pi/4;
                tread_x = [0.7*cos(angle_pos), 0.9*cos(angle_pos)];
                tread_y = [0.7*sin(angle_pos), 0.9*sin(angle_pos)];
                tread_z = [0, 0];
                plot3(app.WearResultAxes, tread_x, tread_y, tread_z, 'k-', 'LineWidth', 3);
            end

            % Set 3D view and lighting
            view(app.WearResultAxes, 45, 30);
            axis(app.WearResultAxes, 'equal');
            lighting(app.WearResultAxes, 'gouraud');
            camlight(app.WearResultAxes, 'headlight');

            % Enhanced title and labels
            app.WearResultAxes.Title.String = sprintf('🔍 轮胎3D磨损仿真 - %s', pattern);
            app.WearResultAxes.Title.FontSize = 14;
            app.WearResultAxes.Title.FontWeight = 'bold';
            app.WearResultAxes.XLabel.String = 'X轴 (m)';
            app.WearResultAxes.YLabel.String = 'Y轴 (m)';
            app.WearResultAxes.ZLabel.String = 'Z轴 (m)';

            % Add colorbar for wear intensity
            c = colorbar(app.WearResultAxes);
            c.Label.String = '磨损强度';
            c.Label.FontSize = 11;

            % Calculate comprehensive analysis
            avg_wear = mean(r_outer - wear_profile);
            max_wear = max(r_outer - wear_profile);
            wear_uniformity = 1 - std(r_outer - wear_profile) / mean(r_outer - wear_profile);

            % Enhanced status evaluation
            if wearDepth > 3.0
                status = '🔴 严重磨损';
                color = [0.8 0.2 0.2];
                recommendation = '立即更换轮胎';
                safety_level = '危险';
            elseif wearDepth > 1.6
                status = '🟡 中度磨损';
                color = [1 0.6 0];
                recommendation = '加强监控';
                safety_level = '警告';
            else
                status = '🟢 轻微磨损';
                color = [0.2 0.7 0.2];
                recommendation = '正常使用';
                safety_level = '安全';
            end

            app.WearStatusLabel.Text = sprintf('%s | 深度: %.1fmm | 面积: %.1f%% | 均匀度: %.1f%% | %s | 建议: %s', ...
                status, wearDepth, wearArea, wear_uniformity*100, safety_level, recommendation);
            app.WearStatusLabel.FontColor = color;
            app.WearStatusLabel.FontWeight = 'bold';
            app.StatusLabel.Text = '🎯 3D轮胎磨损仿真分析完成 - 高级可视化已生成';
        end

        % Button pushed function: MonitorButton
        function MonitorButtonPushed(app, event)
            % Advanced tire pressure monitoring with real-time simulation
            pressure = app.PressureField.Value;
            temperature = app.TemperatureField.Value;
            load = app.LoadField.Value;

            % Clear previous plots
            cla(app.PressureAxes);

            % Create advanced pressure distribution simulation
            % Simulate tire contact patch and pressure distribution
            [x, y] = meshgrid(linspace(-0.15, 0.15, 50), linspace(-0.1, 0.1, 30));

            % Calculate pressure distribution based on load and tire geometry
            contact_ellipse = (x/0.12).^2 + (y/0.08).^2;
            base_pressure = pressure * ones(size(x));

            % Apply load-based pressure variation
            load_factor = load / 500; % Normalize to typical load
            pressure_distribution = base_pressure .* (1 + 0.3 * load_factor * exp(-2*contact_ellipse));

            % Add temperature effects on pressure distribution
            temp_factor = 1 + (temperature - 20) * 0.01; % Temperature coefficient
            pressure_distribution = pressure_distribution * temp_factor;

            % Apply contact patch effects
            contact_mask = contact_ellipse <= 1;
            pressure_distribution(~contact_mask) = 0;

            % Create 3D pressure distribution visualization
            surf(app.PressureAxes, x*1000, y*1000, pressure_distribution, 'EdgeColor', 'none', 'FaceAlpha', 0.9);

            % Add tire outline
            hold(app.PressureAxes, 'on');
            theta_tire = linspace(0, 2*pi, 100);
            x_tire = 120 * cos(theta_tire);
            y_tire = 80 * sin(theta_tire);
            z_tire = zeros(size(theta_tire));
            plot3(app.PressureAxes, x_tire, y_tire, z_tire, 'k-', 'LineWidth', 3);

            % Add pressure sensors simulation (8 points around tire)
            sensor_angles = linspace(0, 2*pi, 9);
            sensor_angles(end) = []; % Remove duplicate point
            for i = 1:length(sensor_angles)
                sx = 100 * cos(sensor_angles(i));
                sy = 70 * sin(sensor_angles(i));
                sz = pressure + 0.1*randn(); % Simulated sensor reading
                plot3(app.PressureAxes, sx, sy, sz, 'ro', 'MarkerSize', 8, 'MarkerFaceColor', 'red');
                text(app.PressureAxes, sx+10, sy+10, sz+0.1, sprintf('%.1f', sz), 'FontSize', 8, 'Color', 'red');
            end

            % Enhanced visualization settings
            colormap(app.PressureAxes, 'jet');
            c = colorbar(app.PressureAxes);
            c.Label.String = '胎压 (bar)';
            c.Label.FontSize = 11;

            % Set labels and title
            app.PressureAxes.XLabel.String = '轮胎宽度 (mm)';
            app.PressureAxes.YLabel.String = '轮胎长度 (mm)';
            app.PressureAxes.ZLabel.String = '压力 (bar)';
            app.PressureAxes.Title.String = '📊 轮胎压力分布仿真监测';
            app.PressureAxes.Title.FontSize = 14;
            app.PressureAxes.Title.FontWeight = 'bold';

            % Set optimal viewing angle
            view(app.PressureAxes, 45, 45);
            lighting(app.PressureAxes, 'gouraud');
            camlight(app.PressureAxes, 'headlight');

            % Calculate advanced metrics
            max_pressure = max(pressure_distribution(:));
            min_pressure = min(pressure_distribution(contact_mask));
            pressure_uniformity = 1 - (max_pressure - min_pressure) / max_pressure;
            contact_area = sum(contact_mask(:)) * (0.3/50) * (0.2/30) * 1000; % cm²

            % Comprehensive status evaluation
            pressure_ok = (pressure >= 2.0 && pressure <= 2.8);
            temp_ok = (temperature >= 10 && temperature <= 40);
            load_ok = (load <= 1500);
            uniformity_ok = (pressure_uniformity > 0.8);

            if pressure_ok && temp_ok && load_ok && uniformity_ok
                status = '🟢 系统正常';
                color = [0.2 0.7 0.2];
                alert = '✓ 最佳状态';
                safety = '安全';
            elseif pressure_ok && temp_ok && load_ok
                status = '🟡 轻微异常';
                color = [1 0.6 0];
                alert = '⚡ 注意监控';
                safety = '警告';
            else
                status = '🔴 异常检测';
                color = [0.8 0.2 0.2];
                alert = '⚠️ 立即检查';
                safety = '危险';
            end

            app.PressureStatusLabel.Text = sprintf('%s | 胎压: %.1fbar | 温度: %.1f°C | 载荷: %dkg | 均匀度: %.1f%% | 接触面积: %.1fcm² | %s', ...
                status, pressure, temperature, load, pressure_uniformity*100, contact_area, alert);
            app.PressureStatusLabel.FontColor = color;
            app.PressureStatusLabel.FontWeight = 'bold';
            app.StatusLabel.Text = '🎯 高级胎压分布仿真完成 - 实时监测数据已更新';
        end

        % Button pushed function: PredictLifeButton
        function PredictLifeButtonPushed(app, event)
            % Advanced integrated warning system with AI simulation
            threshold = app.ThresholdField.Value;
            riskIndex = app.RiskIndexField.Value;

            % Clear previous plots
            cla(app.WarningAxes);

            % Create advanced 3D risk landscape simulation
            wear_range = linspace(0, 10, 60);
            pressure_range = linspace(-1.5, 1.5, 60);
            [W, P] = meshgrid(wear_range, pressure_range);

            % Multi-factor risk calculation with AI-like complexity
            % Base risk factors
            wear_risk = (W / 10).^1.5; % Non-linear wear risk
            pressure_risk = 2 * P.^2; % Quadratic pressure risk

            % Environmental factors
            temp_factor = 0.3 * sin(W/2) .* cos(P*3); % Temperature interaction
            load_factor = 0.2 * exp(-((W-5).^2 + P.^2)/10); % Load distribution

            % Aging and fatigue factors
            fatigue_risk = 0.4 * (W/10).^2 .* (1 + 0.5*sin(P*4));

            % Interaction effects (AI-like non-linear relationships)
            interaction_1 = 0.3 * tanh(W/3) .* exp(-P.^2/0.5);
            interaction_2 = 0.2 * sin(W*P/5) .* cos(W/2);

            % Combined risk with threshold and randomness
            total_risk = (wear_risk + pressure_risk + temp_factor + load_factor + ...
                         fatigue_risk + interaction_1 + interaction_2) * (1 + threshold);

            % Add realistic noise and clamp
            total_risk = total_risk + 0.1 * randn(size(total_risk));
            total_risk = max(0, min(10, total_risk));

            % Create stunning 3D risk landscape
            surf(app.WarningAxes, W, P, total_risk, 'EdgeColor', 'none', 'FaceAlpha', 0.8);

            % Add risk level planes
            hold(app.WarningAxes, 'on');

            % Critical risk plane (red)
            [W_plane, P_plane] = meshgrid(wear_range, pressure_range);
            critical_plane = 7 * ones(size(W_plane));
            surf(app.WarningAxes, W_plane, P_plane, critical_plane, 'FaceColor', 'red', ...
                'FaceAlpha', 0.2, 'EdgeColor', 'red', 'LineWidth', 1);

            % Warning risk plane (yellow)
            warning_plane = 4 * ones(size(W_plane));
            surf(app.WarningAxes, W_plane, P_plane, warning_plane, 'FaceColor', 'yellow', ...
                'FaceAlpha', 0.15, 'EdgeColor', 'yellow', 'LineWidth', 1);

            % Current state marker
            current_wear = 2.5 + riskIndex * 0.3; % Dynamic based on risk index
            current_pressure = 0.1 * sin(riskIndex); % Dynamic pressure deviation
            current_risk = interp2(W, P, total_risk, current_wear, current_pressure, 'linear', riskIndex);

            plot3(app.WarningAxes, current_wear, current_pressure, current_risk, ...
                'ko', 'MarkerSize', 15, 'MarkerFaceColor', 'white', 'LineWidth', 3);

            % Add trajectory prediction (future risk path)
            future_wear = linspace(current_wear, current_wear + 3, 20);
            future_pressure = current_pressure + 0.1*sin(linspace(0, 2*pi, 20));
            future_risk = interp2(W, P, total_risk, future_wear, future_pressure, 'linear', riskIndex);
            plot3(app.WarningAxes, future_wear, future_pressure, future_risk, ...
                'w--', 'LineWidth', 2, 'MarkerSize', 6);

            % Enhanced visualization
            colormap(app.WarningAxes, 'hot');
            c = colorbar(app.WarningAxes);
            c.Label.String = '风险指数 (0-10)';
            c.Label.FontSize = 12;

            % Set labels and title
            app.WarningAxes.XLabel.String = '磨损程度 (mm)';
            app.WarningAxes.YLabel.String = '胎压偏差 (bar)';
            app.WarningAxes.ZLabel.String = '综合风险指数';
            app.WarningAxes.Title.String = '⚠️ AI智能风险预警仿真系统';
            app.WarningAxes.Title.FontSize = 14;
            app.WarningAxes.Title.FontWeight = 'bold';

            % Optimal viewing angle and lighting
            view(app.WarningAxes, 45, 30);
            lighting(app.WarningAxes, 'gouraud');
            camlight(app.WarningAxes, 'headlight');

            % Advanced predictions with AI-like calculations
            predictedLife = max(5, 100 - riskIndex * 7 - (threshold-0.5)*15 - current_risk*2);
            maintenanceInterval = max(0.5, 12 - riskIndex*1.2 - current_risk*0.5);
            replacementTime = max(1, 36 - riskIndex * 3 - current_risk*1.5);
            failureProbability = min(95, riskIndex * 8 + current_risk * 3);

            % Comprehensive status evaluation
            if riskIndex > 7 || current_risk > 7
                status = '🔴 极高风险';
                color = [0.8 0.2 0.2];
                action = '立即停用检修';
                priority = '紧急';
            elseif riskIndex > 5 || current_risk > 5
                status = '🟠 高风险';
                color = [1 0.4 0];
                action = '24小时内检查';
                priority = '高';
            elseif riskIndex > 3 || current_risk > 3
                status = '🟡 中等风险';
                color = [1 0.6 0];
                action = '一周内检查';
                priority = '中';
            else
                status = '🟢 低风险';
                color = [0.2 0.7 0.2];
                action = '正常维护';
                priority = '低';
            end

            app.WarningStatusLabel.Text = sprintf('%s | 风险: %.1f | 寿命: %.0f%% | 故障率: %.1f%% | 维护: %.1f月 | 优先级: %s | %s', ...
                status, current_risk, predictedLife, failureProbability, maintenanceInterval, priority, action);
            app.WarningStatusLabel.FontColor = color;
            app.WarningStatusLabel.FontWeight = 'bold';
            app.StatusLabel.Text = '🎯 AI智能风险预警仿真完成 - 3D风险地图已生成';
        end

        % Button pushed function: GenerateReportButton
        function GenerateReportButtonPushed(app, event)
            % Generate comprehensive analysis report
            dataSource = app.DataSourceDropDown.Value;
            analysisType = app.AnalysisTypeDropDown.Value;

            % Clear previous plots
            cla(app.AnalysisAxes);

            % Create different visualizations based on analysis type
            switch analysisType
                case '趋势分析'
                    % Time series trend analysis
                    time = 1:30; % 30 days
                    wear_trend = 2.5 + 0.05*time + 0.1*sin(time/5) + 0.05*randn(size(time));
                    pressure_trend = 2.4 - 0.01*time + 0.05*cos(time/3) + 0.02*randn(size(time));

                    yyaxis(app.AnalysisAxes, 'left');
                    plot(app.AnalysisAxes, time, wear_trend, 'b-', 'LineWidth', 2, 'Marker', 'o');
                    app.AnalysisAxes.YLabel.String = '磨损深度 (mm)';
                    app.AnalysisAxes.YColor = 'b';

                    yyaxis(app.AnalysisAxes, 'right');
                    plot(app.AnalysisAxes, time, pressure_trend, 'r-', 'LineWidth', 2, 'Marker', 's');
                    app.AnalysisAxes.YLabel.String = '胎压 (bar)';
                    app.AnalysisAxes.YColor = 'r';

                    app.AnalysisAxes.XLabel.String = '时间 (天)';
                    legend(app.AnalysisAxes, {'磨损趋势', '胎压趋势'}, 'Location', 'best');

                case '相关性分析'
                    % Correlation matrix visualization
                    variables = {'磨损深度', '胎压', '温度', '载荷', '风险指数'};
                    corrMatrix = [1.0, -0.6, 0.3, 0.7, 0.8; ...
                                 -0.6, 1.0, -0.2, -0.4, -0.5; ...
                                  0.3, -0.2, 1.0, 0.1, 0.2; ...
                                  0.7, -0.4, 0.1, 1.0, 0.6; ...
                                  0.8, -0.5, 0.2, 0.6, 1.0];

                    imagesc(app.AnalysisAxes, corrMatrix);
                    colormap(app.AnalysisAxes, 'RdBu');
                    colorbar(app.AnalysisAxes);
                    app.AnalysisAxes.XTick = 1:5;
                    app.AnalysisAxes.YTick = 1:5;
                    app.AnalysisAxes.XTickLabel = variables;
                    app.AnalysisAxes.YTickLabel = variables;
                    app.AnalysisAxes.XTickLabelRotation = 45;

                case '预测分析'
                    % Predictive modeling visualization
                    future_time = 1:60; % 60 days prediction
                    historical = 1:30;
                    predicted = 31:60;

                    hist_wear = 2.5 + 0.05*historical + 0.1*randn(size(historical));
                    pred_wear = 2.5 + 0.05*predicted + 0.02*predicted.^1.1;

                    plot(app.AnalysisAxes, historical, hist_wear, 'b-', 'LineWidth', 2, 'Marker', 'o');
                    hold(app.AnalysisAxes, 'on');
                    plot(app.AnalysisAxes, predicted, pred_wear, 'r--', 'LineWidth', 2, 'Marker', 's');

                    % Add confidence intervals
                    upper_bound = pred_wear + 0.2;
                    lower_bound = pred_wear - 0.2;
                    fill(app.AnalysisAxes, [predicted, fliplr(predicted)], [upper_bound, fliplr(lower_bound)], ...
                        'r', 'FaceAlpha', 0.2, 'EdgeColor', 'none');

                    app.AnalysisAxes.XLabel.String = '时间 (天)';
                    app.AnalysisAxes.YLabel.String = '磨损深度 (mm)';
                    legend(app.AnalysisAxes, {'历史数据', '预测数据', '置信区间'}, 'Location', 'best');

                case '异常检测'
                    % Anomaly detection scatter plot
                    normal_data = mvnrnd([2.4, 25], [0.1, 0; 0, 4], 100);
                    anomaly_data = [1.8, 35; 3.2, 15; 2.8, 45]; % Anomalous points

                    scatter(app.AnalysisAxes, normal_data(:,1), normal_data(:,2), 50, 'b', 'filled', 'o');
                    hold(app.AnalysisAxes, 'on');
                    scatter(app.AnalysisAxes, anomaly_data(:,1), anomaly_data(:,2), 100, 'r', 'filled', 's');

                    app.AnalysisAxes.XLabel.String = '胎压 (bar)';
                    app.AnalysisAxes.YLabel.String = '温度 (°C)';
                    legend(app.AnalysisAxes, {'正常数据', '异常数据'}, 'Location', 'best');
            end

            app.AnalysisAxes.Title.String = sprintf('%s - %s分析结果', dataSource, analysisType);
            app.AnalysisAxes.Title.FontSize = 14;
            app.AnalysisAxes.Title.FontWeight = 'bold';
            app.AnalysisAxes.Grid = 'on';

            % Generate enhanced report text
            currentTime = datetime('now');
            reportText = sprintf(['╔══════════════════════════════════════════════════════════════╗\n' ...
                '║                    轮胎状态综合分析报告                      ║\n' ...
                '╚══════════════════════════════════════════════════════════════╝\n\n' ...
                '📊 分析配置\n' ...
                '   • 数据源: %s\n' ...
                '   • 分析类型: %s\n' ...
                '   • 生成时间: %s\n' ...
                '   • 分析周期: 30天\n\n' ...
                '🔍 关键发现\n' ...
                '   1. 磨损状态: 当前磨损深度2.5mm，处于正常范围\n' ...
                '   2. 胎压监测: 平均胎压2.4bar，波动范围±0.1bar\n' ...
                '   3. 温度影响: 环境温度对胎压影响系数0.02bar/°C\n' ...
                '   4. 载荷分析: 载荷变化对磨损加速度影响显著\n\n' ...
                '⚠️  风险评估\n' ...
                '   • 当前风险等级: 中等 (5.0/10)\n' ...
                '   • 预计剩余寿命: 75%%\n' ...
                '   • 建议检查周期: 每月一次\n' ...
                '   • 预计更换时间: 18个月后\n\n' ...
                '📈 趋势预测\n' ...
                '   • 磨损速率: 0.05mm/月 (正常)\n' ...
                '   • 胎压稳定性: 良好\n' ...
                '   • 温度适应性: 优秀\n\n' ...
                '🛠️  维护建议\n' ...
                '   ✓ 保持胎压在2.2-2.6bar范围内\n' ...
                '   ✓ 定期检查轮胎磨损模式\n' ...
                '   ✓ 避免急加速和急刹车\n' ...
                '   ✓ 注意载荷分布均匀性\n' ...
                '   ✓ 高温天气增加检查频率\n\n' ...
                '📞 技术支持: 如有疑问请联系技术团队\n' ...
                '📄 报告编号: RPT-%s'], ...
                dataSource, analysisType, char(currentTime), char(currentTime, 'yyyyMMdd-HHmmss'));

            app.ReportTextArea.Value = reportText;
            app.StatusLabel.Text = sprintf('数据分析完成 - %s报告已生成', analysisType);
        end

        % Show specific page
        function showPage(app, pageNum)
            % Hide all pages first
            if ~isempty(app.Page1Panel) && isvalid(app.Page1Panel)
                app.Page1Panel.Visible = 'off';
            end
            if ~isempty(app.Page2Panel) && isvalid(app.Page2Panel)
                app.Page2Panel.Visible = 'off';
            end
            if ~isempty(app.Page3Panel) && isvalid(app.Page3Panel)
                app.Page3Panel.Visible = 'off';
            end
            if ~isempty(app.Page4Panel) && isvalid(app.Page4Panel)
                app.Page4Panel.Visible = 'off';
            end

            % Reset button colors
            app.Tab1Button.BackgroundColor = [0.25 0.4 0.6];
            app.Tab2Button.BackgroundColor = [0.25 0.4 0.6];
            app.Tab3Button.BackgroundColor = [0.25 0.4 0.6];
            app.Tab4Button.BackgroundColor = [0.25 0.4 0.6];

            % Create and show selected page
            switch pageNum
                case 1
                    createPage1(app);
                    app.Page1Panel.Visible = 'on';
                    app.Tab1Button.BackgroundColor = [0.3 0.5 0.7];
                case 2
                    createPage2(app);
                    app.Page2Panel.Visible = 'on';
                    app.Tab2Button.BackgroundColor = [0.3 0.5 0.7];
                case 3
                    createPage3(app);
                    app.Page3Panel.Visible = 'on';
                    app.Tab3Button.BackgroundColor = [0.3 0.5 0.7];
                case 4
                    createPage4(app);
                    app.Page4Panel.Visible = 'on';
                    app.Tab4Button.BackgroundColor = [0.3 0.5 0.7];
            end

            app.CurrentPage = pageNum;
        end

        % Create Page 1: Tire Wear Recognition
        function createPage1(app)
            if isempty(app.Page1Panel) || ~isvalid(app.Page1Panel)
                app.Page1Panel = uipanel(app.MainGrid);
                app.Page1Panel.Title = '';
                app.Page1Panel.BackgroundColor = [0.98 0.98 1];
                app.Page1Panel.Layout.Row = 1;
                app.Page1Panel.Layout.Column = 1;

                app.Page1Grid = uigridlayout(app.Page1Panel);
                app.Page1Grid.ColumnWidth = {'1x', '2x'}; % 1:2的比例，左侧控件区更宽
                app.Page1Grid.RowHeight = {'1x'};
                app.Page1Grid.Padding = [20 20 20 20];
                app.Page1Grid.ColumnSpacing = 25;

                % 创建左侧控件面板
                leftPanel = uipanel(app.Page1Grid);
                leftPanel.Title = '';
                leftPanel.BackgroundColor = [0.95 0.97 1];
                leftPanel.Layout.Row = 1;
                leftPanel.Layout.Column = 1;

                leftGrid = uigridlayout(leftPanel);
                leftGrid.ColumnWidth = {120, '1x'};
                leftGrid.RowHeight = {60, 60, 60, 80, 60, '1x'};
                leftGrid.Padding = [20 20 20 20];
                leftGrid.RowSpacing = 25;
                leftGrid.ColumnSpacing = 15;

                % Wear depth input
                app.WearDepthLabel = uilabel(leftGrid);
                app.WearDepthLabel.Text = '🔧 磨损深度:';
                app.WearDepthLabel.FontWeight = 'bold';
                app.WearDepthLabel.FontSize = 14;
                app.WearDepthLabel.FontColor = [0.2 0.3 0.5];
                app.WearDepthLabel.Layout.Row = 1;
                app.WearDepthLabel.Layout.Column = 1;

                app.WearDepthField = uieditfield(leftGrid, 'numeric');
                app.WearDepthField.Value = 2.5;
                app.WearDepthField.Limits = [0 10];
                app.WearDepthField.FontSize = 13;
                app.WearDepthField.BackgroundColor = [1 1 1];
                app.WearDepthField.Layout.Row = 1;
                app.WearDepthField.Layout.Column = 2;

                % Note: Unit labels removed to avoid GridLayout position warnings

                % Wear area input
                app.WearAreaLabel = uilabel(leftGrid);
                app.WearAreaLabel.Text = '📊 磨损面积:';
                app.WearAreaLabel.FontWeight = 'bold';
                app.WearAreaLabel.FontSize = 14;
                app.WearAreaLabel.FontColor = [0.2 0.3 0.5];
                app.WearAreaLabel.Layout.Row = 2;
                app.WearAreaLabel.Layout.Column = 1;

                app.WearAreaField = uieditfield(leftGrid, 'numeric');
                app.WearAreaField.Value = 15.0;
                app.WearAreaField.Limits = [0 100];
                app.WearAreaField.FontSize = 13;
                app.WearAreaField.BackgroundColor = [1 1 1];
                app.WearAreaField.Layout.Row = 2;
                app.WearAreaField.Layout.Column = 2;

                % Wear pattern dropdown
                app.WearPatternLabel = uilabel(leftGrid);
                app.WearPatternLabel.Text = '🔍 磨损模式:';
                app.WearPatternLabel.FontWeight = 'bold';
                app.WearPatternLabel.FontSize = 14;
                app.WearPatternLabel.FontColor = [0.2 0.3 0.5];
                app.WearPatternLabel.Layout.Row = 3;
                app.WearPatternLabel.Layout.Column = 1;

                app.WearPatternDropDown = uidropdown(leftGrid);
                app.WearPatternDropDown.Items = {'均匀磨损', '偏磨', '中心磨损', '边缘磨损', '斑点磨损'};
                app.WearPatternDropDown.Value = '均匀磨损';
                app.WearPatternDropDown.FontSize = 13;
                app.WearPatternDropDown.BackgroundColor = [1 1 1];
                app.WearPatternDropDown.Layout.Row = 3;
                app.WearPatternDropDown.Layout.Column = 2;

                % Analyze button
                app.AnalyzeWearButton = uibutton(leftGrid, 'push');
                app.AnalyzeWearButton.ButtonPushedFcn = createCallbackFcn(app, @AnalyzeWearButtonPushed, true);
                app.AnalyzeWearButton.Text = '🚀 开始磨损分析';
                app.AnalyzeWearButton.BackgroundColor = [0.2 0.6 0.8];
                app.AnalyzeWearButton.FontColor = [1 1 1];
                app.AnalyzeWearButton.FontWeight = 'bold';
                app.AnalyzeWearButton.FontSize = 14;
                app.AnalyzeWearButton.Layout.Row = 4;
                app.AnalyzeWearButton.Layout.Column = [1 2];

                % Status label in left panel
                app.WearStatusLabel = uilabel(leftGrid);
                app.WearStatusLabel.Text = '💡 请输入参数并点击分析按钮开始检测...';
                app.WearStatusLabel.FontSize = 11;
                app.WearStatusLabel.FontColor = [0.4 0.4 0.6];
                app.WearStatusLabel.WordWrap = 'on';
                app.WearStatusLabel.Layout.Row = 5;
                app.WearStatusLabel.Layout.Column = [1 2];

                % Results axes in right panel
                app.WearResultAxes = uiaxes(app.Page1Grid);
                app.WearResultAxes.Layout.Row = 1;
                app.WearResultAxes.Layout.Column = 2;
                app.WearResultAxes.Title.String = '轮胎磨损可视化分析';
                app.WearResultAxes.Title.FontSize = 16;
                app.WearResultAxes.Title.FontWeight = 'bold';
                app.WearResultAxes.BackgroundColor = [0.98 0.98 1];
            end
        end
    end

    % Component initialization
    methods (Access = private)

        % Create UIFigure and components
        function createComponents(app)

            % Create UIFigure and hide until all components are created
            app.UIFigure = uifigure('Visible', 'off');
            app.UIFigure.Position = [100 100 1200 800];
            app.UIFigure.Name = '轮胎磨损状态识别与胎压异常联动预警平台';
            app.UIFigure.Resize = 'on';

            % Create GridLayout with enhanced proportions
            app.GridLayout = uigridlayout(app.UIFigure);
            app.GridLayout.ColumnWidth = {250, '1x'}; % Wider navigation panel
            app.GridLayout.RowHeight = {'1x', 35}; % Taller status bar

            % Create NavigationPanel with gradient-like effect
            app.NavigationPanel = uipanel(app.GridLayout);
            app.NavigationPanel.Title = '';
            app.NavigationPanel.BackgroundColor = [0.15 0.25 0.35]; % Darker, more professional
            app.NavigationPanel.Layout.Row = 1;
            app.NavigationPanel.Layout.Column = 1;

            % Create NavigationGrid with better spacing
            app.NavigationGrid = uigridlayout(app.NavigationPanel);
            app.NavigationGrid.ColumnWidth = {'1x'};
            app.NavigationGrid.RowHeight = {80, 60, 60, 60, 60, '1x'}; % Larger buttons
            app.NavigationGrid.Padding = [15 15 15 15]; % More padding
            app.NavigationGrid.RowSpacing = 20; % Increased spacing

            % Create TitleLabel
            app.TitleLabel = uilabel(app.NavigationGrid);
            app.TitleLabel.FontSize = 16;
            app.TitleLabel.FontWeight = 'bold';
            app.TitleLabel.FontColor = [1 1 1];
            app.TitleLabel.Text = '🚗 轮胎智能预警平台';
            app.TitleLabel.HorizontalAlignment = 'center';
            app.TitleLabel.Layout.Row = 1;
            app.TitleLabel.Layout.Column = 1;

            % Create Tab1Button
            app.Tab1Button = uibutton(app.NavigationGrid, 'push');
            app.Tab1Button.ButtonPushedFcn = createCallbackFcn(app, @Tab1ButtonPushed, true);
            app.Tab1Button.BackgroundColor = [0.3 0.5 0.7];
            app.Tab1Button.FontColor = [1 1 1];
            app.Tab1Button.FontSize = 13;
            app.Tab1Button.FontWeight = 'bold';
            app.Tab1Button.Text = '🔍 磨损识别';
            app.Tab1Button.Layout.Row = 2;
            app.Tab1Button.Layout.Column = 1;

            % Create Tab2Button
            app.Tab2Button = uibutton(app.NavigationGrid, 'push');
            app.Tab2Button.ButtonPushedFcn = createCallbackFcn(app, @Tab2ButtonPushed, true);
            app.Tab2Button.BackgroundColor = [0.25 0.4 0.6];
            app.Tab2Button.FontColor = [1 1 1];
            app.Tab2Button.FontSize = 13;
            app.Tab2Button.FontWeight = 'bold';
            app.Tab2Button.Text = '📊 胎压监测';
            app.Tab2Button.Layout.Row = 3;
            app.Tab2Button.Layout.Column = 1;

            % Create Tab3Button
            app.Tab3Button = uibutton(app.NavigationGrid, 'push');
            app.Tab3Button.ButtonPushedFcn = createCallbackFcn(app, @Tab3ButtonPushed, true);
            app.Tab3Button.BackgroundColor = [0.25 0.4 0.6];
            app.Tab3Button.FontColor = [1 1 1];
            app.Tab3Button.FontSize = 13;
            app.Tab3Button.FontWeight = 'bold';
            app.Tab3Button.Text = '⚠️ 联动预警';
            app.Tab3Button.Layout.Row = 4;
            app.Tab3Button.Layout.Column = 1;

            % Create Tab4Button
            app.Tab4Button = uibutton(app.NavigationGrid, 'push');
            app.Tab4Button.ButtonPushedFcn = createCallbackFcn(app, @Tab4ButtonPushed, true);
            app.Tab4Button.BackgroundColor = [0.25 0.4 0.6];
            app.Tab4Button.FontColor = [1 1 1];
            app.Tab4Button.FontSize = 13;
            app.Tab4Button.FontWeight = 'bold';
            app.Tab4Button.Text = '📈 数据分析';
            app.Tab4Button.Layout.Row = 5;
            app.Tab4Button.Layout.Column = 1;

            % Create MainPanel with professional gradient background
            app.MainPanel = uipanel(app.GridLayout);
            app.MainPanel.Title = '';
            app.MainPanel.BackgroundColor = [0.96 0.97 0.99]; % Subtle blue-white gradient
            app.MainPanel.Layout.Row = 1;
            app.MainPanel.Layout.Column = 2;

            % Create MainGrid with no padding for full coverage
            app.MainGrid = uigridlayout(app.MainPanel);
            app.MainGrid.ColumnWidth = {'1x'};
            app.MainGrid.RowHeight = {'1x'};
            app.MainGrid.Padding = [0 0 0 0];

            % Create enhanced StatusPanel
            app.StatusPanel = uipanel(app.GridLayout);
            app.StatusPanel.Title = '';
            app.StatusPanel.BackgroundColor = [0.85 0.88 0.92]; % Professional gray-blue
            app.StatusPanel.Layout.Row = 2;
            app.StatusPanel.Layout.Column = [1 2];

            % Create enhanced StatusLabel
            app.StatusLabel = uilabel(app.StatusPanel);
            app.StatusLabel.Text = '🚀 系统已启动 - 轮胎智能预警平台就绪';
            app.StatusLabel.Position = [15 8 1200 25]; % Better positioning
            app.StatusLabel.FontSize = 12;
            app.StatusLabel.FontWeight = 'bold';
            app.StatusLabel.FontColor = [0.2 0.3 0.5];

            % Show the figure after all components are created
            app.UIFigure.Visible = 'on';
        end

        % Create Page 2: Pressure Monitoring
        function createPage2(app)
            if isempty(app.Page2Panel) || ~isvalid(app.Page2Panel)
                app.Page2Panel = uipanel(app.MainGrid);
                app.Page2Panel.Title = '';
                app.Page2Panel.BackgroundColor = [1 1 1];
                app.Page2Panel.Layout.Row = 1;
                app.Page2Panel.Layout.Column = 1;

                app.Page2Grid = uigridlayout(app.Page2Panel);
                app.Page2Grid.ColumnWidth = {'1x', '2x'}; % 1:2的比例
                app.Page2Grid.RowHeight = {'1x'};
                app.Page2Grid.Padding = [20 20 20 20];
                app.Page2Grid.ColumnSpacing = 25;

                % 创建左侧控件面板
                leftPanel2 = uipanel(app.Page2Grid);
                leftPanel2.Title = '';
                leftPanel2.BackgroundColor = [0.95 0.97 1];
                leftPanel2.Layout.Row = 1;
                leftPanel2.Layout.Column = 1;

                leftGrid2 = uigridlayout(leftPanel2);
                leftGrid2.ColumnWidth = {100, '1x'};
                leftGrid2.RowHeight = {60, 60, 60, 80, 60, '1x'};
                leftGrid2.Padding = [20 20 20 20];
                leftGrid2.RowSpacing = 25;
                leftGrid2.ColumnSpacing = 15;

                % Pressure input
                app.PressureLabel = uilabel(leftGrid2);
                app.PressureLabel.Text = '📊 胎压:';
                app.PressureLabel.FontWeight = 'bold';
                app.PressureLabel.FontSize = 14;
                app.PressureLabel.FontColor = [0.2 0.3 0.5];
                app.PressureLabel.Layout.Row = 1;
                app.PressureLabel.Layout.Column = 1;

                app.PressureField = uieditfield(leftGrid2, 'numeric');
                app.PressureField.Value = 2.4;
                app.PressureField.Limits = [0 5];
                app.PressureField.FontSize = 13;
                app.PressureField.BackgroundColor = [1 1 1];
                app.PressureField.Layout.Row = 1;
                app.PressureField.Layout.Column = 2;

                % Temperature input
                app.TemperatureLabel = uilabel(leftGrid2);
                app.TemperatureLabel.Text = '🌡️ 温度:';
                app.TemperatureLabel.FontWeight = 'bold';
                app.TemperatureLabel.FontSize = 14;
                app.TemperatureLabel.FontColor = [0.2 0.3 0.5];
                app.TemperatureLabel.Layout.Row = 2;
                app.TemperatureLabel.Layout.Column = 1;

                app.TemperatureField = uieditfield(leftGrid2, 'numeric');
                app.TemperatureField.Value = 25.0;
                app.TemperatureField.Limits = [-20 80];
                app.TemperatureField.FontSize = 13;
                app.TemperatureField.BackgroundColor = [1 1 1];
                app.TemperatureField.Layout.Row = 2;
                app.TemperatureField.Layout.Column = 2;

                % Load input
                app.LoadLabel = uilabel(leftGrid2);
                app.LoadLabel.Text = '⚖️ 载荷:';
                app.LoadLabel.FontWeight = 'bold';
                app.LoadLabel.FontSize = 14;
                app.LoadLabel.FontColor = [0.2 0.3 0.5];
                app.LoadLabel.Layout.Row = 3;
                app.LoadLabel.Layout.Column = 1;

                app.LoadField = uieditfield(leftGrid2, 'numeric');
                app.LoadField.Value = 500;
                app.LoadField.Limits = [0 2000];
                app.LoadField.FontSize = 13;
                app.LoadField.BackgroundColor = [1 1 1];
                app.LoadField.Layout.Row = 3;
                app.LoadField.Layout.Column = 2;

                % Monitor button
                app.MonitorButton = uibutton(leftGrid2, 'push');
                app.MonitorButton.ButtonPushedFcn = createCallbackFcn(app, @MonitorButtonPushed, true);
                app.MonitorButton.Text = '🚀 开始胎压监测';
                app.MonitorButton.BackgroundColor = [0.2 0.7 0.3];
                app.MonitorButton.FontColor = [1 1 1];
                app.MonitorButton.FontWeight = 'bold';
                app.MonitorButton.FontSize = 14;
                app.MonitorButton.Layout.Row = 4;
                app.MonitorButton.Layout.Column = [1 2];

                % Status label in left panel
                app.PressureStatusLabel = uilabel(leftGrid2);
                app.PressureStatusLabel.Text = '💡 请输入参数并点击监测按钮开始分析...';
                app.PressureStatusLabel.FontSize = 11;
                app.PressureStatusLabel.FontColor = [0.4 0.4 0.6];
                app.PressureStatusLabel.WordWrap = 'on';
                app.PressureStatusLabel.Layout.Row = 5;
                app.PressureStatusLabel.Layout.Column = [1 2];

                % Results axes in right panel
                app.PressureAxes = uiaxes(app.Page2Grid);
                app.PressureAxes.Layout.Row = 1;
                app.PressureAxes.Layout.Column = 2;
                app.PressureAxes.Title.String = '胎压分布仿真监测';
                app.PressureAxes.Title.FontSize = 16;
                app.PressureAxes.Title.FontWeight = 'bold';
                app.PressureAxes.BackgroundColor = [0.98 0.98 1];
            end
        end

        % Create Page 3: Integrated Warning
        function createPage3(app)
            if isempty(app.Page3Panel) || ~isvalid(app.Page3Panel)
                app.Page3Panel = uipanel(app.MainGrid);
                app.Page3Panel.Title = '';
                app.Page3Panel.BackgroundColor = [1 1 1];
                app.Page3Panel.Layout.Row = 1;
                app.Page3Panel.Layout.Column = 1;

                app.Page3Grid = uigridlayout(app.Page3Panel);
                app.Page3Grid.ColumnWidth = {'1x', '2x'}; % 1:2的比例
                app.Page3Grid.RowHeight = {'1x'};
                app.Page3Grid.Padding = [20 20 20 20];
                app.Page3Grid.ColumnSpacing = 25;

                % 创建左侧控件面板
                leftPanel3 = uipanel(app.Page3Grid);
                leftPanel3.Title = '';
                leftPanel3.BackgroundColor = [0.95 0.97 1];
                leftPanel3.Layout.Row = 1;
                leftPanel3.Layout.Column = 1;

                leftGrid3 = uigridlayout(leftPanel3);
                leftGrid3.ColumnWidth = {120, '1x'};
                leftGrid3.RowHeight = {60, 60, 80, 60, '1x'};
                leftGrid3.Padding = [20 20 20 20];
                leftGrid3.RowSpacing = 25;
                leftGrid3.ColumnSpacing = 15;

                % Threshold input
                app.ThresholdLabel = uilabel(leftGrid3);
                app.ThresholdLabel.Text = '⚠️ 预警阈值:';
                app.ThresholdLabel.FontWeight = 'bold';
                app.ThresholdLabel.FontSize = 14;
                app.ThresholdLabel.FontColor = [0.2 0.3 0.5];
                app.ThresholdLabel.Layout.Row = 1;
                app.ThresholdLabel.Layout.Column = 1;

                app.ThresholdField = uieditfield(leftGrid3, 'numeric');
                app.ThresholdField.Value = 0.75;
                app.ThresholdField.Limits = [0 1];
                app.ThresholdField.FontSize = 13;
                app.ThresholdField.BackgroundColor = [1 1 1];
                app.ThresholdField.Layout.Row = 1;
                app.ThresholdField.Layout.Column = 2;

                % Risk index input
                app.RiskIndexLabel = uilabel(leftGrid3);
                app.RiskIndexLabel.Text = '📊 风险指数:';
                app.RiskIndexLabel.FontWeight = 'bold';
                app.RiskIndexLabel.FontSize = 14;
                app.RiskIndexLabel.FontColor = [0.2 0.3 0.5];
                app.RiskIndexLabel.Layout.Row = 2;
                app.RiskIndexLabel.Layout.Column = 1;

                app.RiskIndexField = uieditfield(leftGrid3, 'numeric');
                app.RiskIndexField.Value = 5.0;
                app.RiskIndexField.Limits = [0 10];
                app.RiskIndexField.FontSize = 13;
                app.RiskIndexField.BackgroundColor = [1 1 1];
                app.RiskIndexField.Layout.Row = 2;
                app.RiskIndexField.Layout.Column = 2;

                % Predict button
                app.PredictLifeButton = uibutton(leftGrid3, 'push');
                app.PredictLifeButton.ButtonPushedFcn = createCallbackFcn(app, @PredictLifeButtonPushed, true);
                app.PredictLifeButton.Text = '🚀 AI风险评估';
                app.PredictLifeButton.BackgroundColor = [0.8 0.4 0.2];
                app.PredictLifeButton.FontColor = [1 1 1];
                app.PredictLifeButton.FontWeight = 'bold';
                app.PredictLifeButton.FontSize = 14;
                app.PredictLifeButton.Layout.Row = 3;
                app.PredictLifeButton.Layout.Column = [1 2];

                % Status label in left panel
                app.WarningStatusLabel = uilabel(leftGrid3);
                app.WarningStatusLabel.Text = '💡 请输入参数并点击评估按钮开始分析...';
                app.WarningStatusLabel.FontSize = 11;
                app.WarningStatusLabel.FontColor = [0.4 0.4 0.6];
                app.WarningStatusLabel.WordWrap = 'on';
                app.WarningStatusLabel.Layout.Row = 4;
                app.WarningStatusLabel.Layout.Column = [1 2];

                % Results axes in right panel
                app.WarningAxes = uiaxes(app.Page3Grid);
                app.WarningAxes.Layout.Row = 1;
                app.WarningAxes.Layout.Column = 2;
                app.WarningAxes.Title.String = 'AI智能风险预警仿真';
                app.WarningAxes.Title.FontSize = 16;
                app.WarningAxes.Title.FontWeight = 'bold';
                app.WarningAxes.BackgroundColor = [0.98 0.98 1];
            end
        end

        % Create Page 4: Data Analysis
        function createPage4(app)
            if isempty(app.Page4Panel) || ~isvalid(app.Page4Panel)
                app.Page4Panel = uipanel(app.MainGrid);
                app.Page4Panel.Title = '';
                app.Page4Panel.BackgroundColor = [1 1 1];
                app.Page4Panel.Layout.Row = 1;
                app.Page4Panel.Layout.Column = 1;

                app.Page4Grid = uigridlayout(app.Page4Panel);
                app.Page4Grid.ColumnWidth = {'1x', '1x'}; % 1:1的比例，上下分布
                app.Page4Grid.RowHeight = {'1x', '1x'};
                app.Page4Grid.Padding = [20 20 20 20];
                app.Page4Grid.RowSpacing = 20;
                app.Page4Grid.ColumnSpacing = 25;

                % 创建上方控件和图表区域
                topPanel = uipanel(app.Page4Grid);
                topPanel.Title = '';
                topPanel.BackgroundColor = [0.95 0.97 1];
                topPanel.Layout.Row = 1;
                topPanel.Layout.Column = [1 2];

                topGrid = uigridlayout(topPanel);
                topGrid.ColumnWidth = {'1x', '2x'}; % 左侧控件，右侧图表
                topGrid.RowHeight = {'1x'};
                topGrid.Padding = [15 15 15 15];
                topGrid.ColumnSpacing = 20;

                % 左侧控件面板
                leftPanel4 = uipanel(topGrid);
                leftPanel4.Title = '';
                leftPanel4.BackgroundColor = [0.92 0.95 1];
                leftPanel4.Layout.Row = 1;
                leftPanel4.Layout.Column = 1;

                leftGrid4 = uigridlayout(leftPanel4);
                leftGrid4.ColumnWidth = {100, '1x'};
                leftGrid4.RowHeight = {60, 60, 80, '1x'};
                leftGrid4.Padding = [15 15 15 15];
                leftGrid4.RowSpacing = 20;
                leftGrid4.ColumnSpacing = 10;

                % Data source dropdown
                app.DataSourceLabel = uilabel(leftGrid4);
                app.DataSourceLabel.Text = '📂 数据源:';
                app.DataSourceLabel.FontWeight = 'bold';
                app.DataSourceLabel.FontSize = 14;
                app.DataSourceLabel.FontColor = [0.2 0.3 0.5];
                app.DataSourceLabel.Layout.Row = 1;
                app.DataSourceLabel.Layout.Column = 1;

                app.DataSourceDropDown = uidropdown(leftGrid4);
                app.DataSourceDropDown.Items = {'实时数据', '历史数据', '模拟数据'};
                app.DataSourceDropDown.Value = '实时数据';
                app.DataSourceDropDown.FontSize = 13;
                app.DataSourceDropDown.BackgroundColor = [1 1 1];
                app.DataSourceDropDown.Layout.Row = 1;
                app.DataSourceDropDown.Layout.Column = 2;

                % Analysis type dropdown
                app.AnalysisTypeLabel = uilabel(leftGrid4);
                app.AnalysisTypeLabel.Text = '📈 分析类型:';
                app.AnalysisTypeLabel.FontWeight = 'bold';
                app.AnalysisTypeLabel.FontSize = 14;
                app.AnalysisTypeLabel.FontColor = [0.2 0.3 0.5];
                app.AnalysisTypeLabel.Layout.Row = 2;
                app.AnalysisTypeLabel.Layout.Column = 1;

                app.AnalysisTypeDropDown = uidropdown(leftGrid4);
                app.AnalysisTypeDropDown.Items = {'趋势分析', '相关性分析', '预测分析', '异常检测'};
                app.AnalysisTypeDropDown.Value = '趋势分析';
                app.AnalysisTypeDropDown.FontSize = 13;
                app.AnalysisTypeDropDown.BackgroundColor = [1 1 1];
                app.AnalysisTypeDropDown.Layout.Row = 2;
                app.AnalysisTypeDropDown.Layout.Column = 2;

                % Generate report button
                app.GenerateReportButton = uibutton(leftGrid4, 'push');
                app.GenerateReportButton.ButtonPushedFcn = createCallbackFcn(app, @GenerateReportButtonPushed, true);
                app.GenerateReportButton.Text = '🚀 生成分析报告';
                app.GenerateReportButton.BackgroundColor = [0.5 0.2 0.8];
                app.GenerateReportButton.FontColor = [1 1 1];
                app.GenerateReportButton.FontWeight = 'bold';
                app.GenerateReportButton.FontSize = 14;
                app.GenerateReportButton.Layout.Row = 3;
                app.GenerateReportButton.Layout.Column = [1 2];

                % Analysis axes in right panel of top section
                app.AnalysisAxes = uiaxes(topGrid);
                app.AnalysisAxes.Layout.Row = 1;
                app.AnalysisAxes.Layout.Column = 2;
                app.AnalysisAxes.Title.String = '数据分析可视化';
                app.AnalysisAxes.Title.FontSize = 16;
                app.AnalysisAxes.Title.FontWeight = 'bold';
                app.AnalysisAxes.BackgroundColor = [0.98 0.98 1];

                % Report text area in bottom section
                app.ReportTextArea = uitextarea(app.Page4Grid);
                app.ReportTextArea.Layout.Row = 2;
                app.ReportTextArea.Layout.Column = [1 2];
                app.ReportTextArea.Value = '💡 分析报告将在此显示...请选择数据源和分析类型后点击生成报告按钮';
                app.ReportTextArea.FontSize = 11;
                app.ReportTextArea.BackgroundColor = [0.98 0.98 1];
            end
        end
    end

    % App creation and deletion
    methods (Access = public)

        % Construct app
        function app = TireWearPressureWarningPlatform

            % Create UIFigure and components
            createComponents(app)

            % Register the app with App Designer
            registerApp(app, app.UIFigure)

            % Execute the startup function
            runStartupFcn(app, @startupFcn)

            if nargout == 0
                clear app
            end
        end

        % Code that executes before app deletion
        function delete(app)

            % Delete UIFigure when app is deleted
            delete(app.UIFigure)
        end
    end
end
