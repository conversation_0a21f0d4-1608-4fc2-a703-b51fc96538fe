classdef TireWearPressureWarningPlatform < matlab.apps.AppBase

    % Properties that correspond to app components
    properties (Access = public)
        UIFigure                        matlab.ui.Figure
        GridLayout                      matlab.ui.container.GridLayout
        
        % Navigation Panel
        NavigationPanel                 matlab.ui.container.Panel
        NavigationGrid                  matlab.ui.container.GridLayout
        TitleLabel                      matlab.ui.control.Label
        Tab1Button                      matlab.ui.control.Button
        Tab2Button                      matlab.ui.control.Button
        Tab3Button                      matlab.ui.control.Button
        Tab4Button                      matlab.ui.control.Button
        
        % Main Content Panel
        MainPanel                       matlab.ui.container.Panel
        MainGrid                        matlab.ui.container.GridLayout
        
        % Page 1: 轮胎磨损识别
        Page1Panel                      matlab.ui.container.Panel
        Page1Grid                       matlab.ui.container.GridLayout
        WearDepthLabel                  matlab.ui.control.Label
        WearDepthField                  matlab.ui.control.NumericEditField
        WearAreaLabel                   matlab.ui.control.Label
        WearAreaField                   matlab.ui.control.NumericEditField
        WearPatternLabel                matlab.ui.control.Label
        WearPatternDropDown             matlab.ui.control.DropDown
        AnalyzeWearButton               matlab.ui.control.Button
        WearResultAxes                  matlab.ui.control.UIAxes
        WearStatusLabel                 matlab.ui.control.Label
        
        % Page 2: 胎压监测
        Page2Panel                      matlab.ui.container.Panel
        Page2Grid                       matlab.ui.container.GridLayout
        PressureLabel                   matlab.ui.control.Label
        PressureField                   matlab.ui.control.NumericEditField
        TemperatureLabel                matlab.ui.control.Label
        TemperatureField                matlab.ui.control.NumericEditField
        LoadLabel                       matlab.ui.control.Label
        LoadField                       matlab.ui.control.NumericEditField
        MonitorButton                   matlab.ui.control.Button
        PressureAxes                    matlab.ui.control.UIAxes
        PressureStatusLabel             matlab.ui.control.Label
        
        % Page 3: 联动预警
        Page3Panel                      matlab.ui.container.Panel
        Page3Grid                       matlab.ui.container.GridLayout
        ThresholdLabel                  matlab.ui.control.Label
        ThresholdField                  matlab.ui.control.NumericEditField
        RiskIndexLabel                  matlab.ui.control.Label
        RiskIndexField                  matlab.ui.control.NumericEditField
        PredictLifeButton               matlab.ui.control.Button
        WarningAxes                     matlab.ui.control.UIAxes
        WarningStatusLabel              matlab.ui.control.Label
        
        % Page 4: 数据分析
        Page4Panel                      matlab.ui.container.Panel
        Page4Grid                       matlab.ui.container.GridLayout
        DataSourceLabel                 matlab.ui.control.Label
        DataSourceDropDown              matlab.ui.control.DropDown
        AnalysisTypeLabel               matlab.ui.control.Label
        AnalysisTypeDropDown            matlab.ui.control.DropDown
        GenerateReportButton            matlab.ui.control.Button
        AnalysisAxes                    matlab.ui.control.UIAxes
        ReportTextArea                  matlab.ui.control.TextArea
        
        % Status Bar
        StatusPanel                     matlab.ui.container.Panel
        StatusLabel                     matlab.ui.control.Label
    end
    
    properties (Access = private)
        CurrentPage = 1  % Current active page
        WearData = []    % Tire wear data
        PressureData = [] % Pressure monitoring data
        WarningData = []  % Warning system data
    end

    % Callbacks that handle component events
    methods (Access = private)

        % Code that executes after component creation
        function startupFcn(app)
            % Initialize the app
            app.UIFigure.Name = '轮胎磨损状态识别与胎压异常联动预警平台';
            app.StatusLabel.Text = '系统已启动，请选择功能模块';
            
            % Set modern color scheme
            app.UIFigure.Color = [0.95 0.95 0.97];
            app.NavigationPanel.BackgroundColor = [0.2 0.3 0.4];
            app.MainPanel.BackgroundColor = [1 1 1];
            
            % Initialize with first page
            showPage(app, 1);
        end

        % Button pushed function: Tab1Button
        function Tab1ButtonPushed(app, event)
            showPage(app, 1);
        end

        % Button pushed function: Tab2Button
        function Tab2ButtonPushed(app, event)
            showPage(app, 2);
        end

        % Button pushed function: Tab3Button
        function Tab3ButtonPushed(app, event)
            showPage(app, 3);
        end

        % Button pushed function: Tab4Button
        function Tab4ButtonPushed(app, event)
            showPage(app, 4);
        end

        % Button pushed function: AnalyzeWearButton
        function AnalyzeWearButtonPushed(app, event)
            % Simulate tire wear analysis
            wearDepth = app.WearDepthField.Value;
            wearArea = app.WearAreaField.Value;
            pattern = app.WearPatternDropDown.Value;
            
            % Generate synthetic wear analysis data
            angles = linspace(0, 2*pi, 100);
            radius = 1 + 0.3*sin(5*angles) + 0.1*randn(1,100);
            
            % Create polar plot for tire wear visualization
            polarplot(app.WearResultAxes, angles, radius, 'LineWidth', 2, 'Color', [0.8 0.2 0.2]);
            hold(app.WearResultAxes, 'on');
            
            % Add wear pattern overlay
            if strcmp(pattern, '偏磨')
                polarplot(app.WearResultAxes, angles(1:50), radius(1:50)*0.8, 'LineWidth', 3, 'Color', [1 0.5 0]);
            end
            
            app.WearResultAxes.Title.String = sprintf('轮胎磨损分析 - %s', pattern);
            
            % Update status
            if wearDepth > 3.0
                status = '严重磨损';
                color = [0.8 0.2 0.2];
            elseif wearDepth > 1.6
                status = '中度磨损';
                color = [1 0.6 0];
            else
                status = '轻微磨损';
                color = [0.2 0.7 0.2];
            end
            
            app.WearStatusLabel.Text = sprintf('磨损状态: %s (深度: %.1fmm, 面积: %.1f%%)', status, wearDepth, wearArea);
            app.WearStatusLabel.FontColor = color;
            app.StatusLabel.Text = '轮胎磨损分析完成';
        end

        % Button pushed function: MonitorButton
        function MonitorButtonPushed(app, event)
            % Simulate pressure monitoring
            pressure = app.PressureField.Value;
            temperature = app.TemperatureField.Value;
            load = app.LoadField.Value;
            
            % Generate time series data
            t = linspace(0, 24, 100);
            pressureData = pressure + 0.1*sin(t/2) + 0.05*randn(1,100);
            tempData = temperature + 2*sin(t/3) + 0.5*randn(1,100);
            
            % Create surface plot for pressure-temperature relationship
            [T, P] = meshgrid(linspace(temperature-5, temperature+5, 20), ...
                             linspace(pressure-0.5, pressure+0.5, 20));
            Z = sin(T/10) .* cos(P*5) + 0.1*randn(20,20);
            
            surf(app.PressureAxes, T, P, Z, 'EdgeColor', 'none', 'FaceAlpha', 0.8);
            app.PressureAxes.XLabel.String = '温度 (°C)';
            app.PressureAxes.YLabel.String = '胎压 (bar)';
            app.PressureAxes.ZLabel.String = '稳定性指数';
            app.PressureAxes.Title.String = '胎压-温度关系分析';
            colormap(app.PressureAxes, 'turbo');
            
            % Update status
            if pressure < 2.0 || pressure > 2.8
                status = '胎压异常';
                color = [0.8 0.2 0.2];
            else
                status = '胎压正常';
                color = [0.2 0.7 0.2];
            end
            
            app.PressureStatusLabel.Text = sprintf('监测状态: %s (胎压: %.1fbar, 温度: %.1f°C)', status, pressure, temperature);
            app.PressureStatusLabel.FontColor = color;
            app.StatusLabel.Text = '胎压监测完成';
        end

        % Button pushed function: PredictLifeButton
        function PredictLifeButtonPushed(app, event)
            % Simulate integrated warning analysis
            threshold = app.ThresholdField.Value;
            riskIndex = app.RiskIndexField.Value;

            % Generate risk assessment heatmap
            [X, Y] = meshgrid(linspace(0, 10, 50), linspace(0, 10, 50));
            Z = exp(-((X-5).^2 + (Y-5).^2)/10) + 0.3*sin(X).*cos(Y) + 0.1*randn(50,50);

            imagesc(app.WarningAxes, Z);
            colormap(app.WarningAxes, 'hot');
            colorbar(app.WarningAxes);
            app.WarningAxes.Title.String = '风险评估热力图';
            app.WarningAxes.XLabel.String = '磨损程度';
            app.WarningAxes.YLabel.String = '胎压偏差';

            % Calculate predicted life
            predictedLife = max(0, 100 - riskIndex * 10);

            % Update status
            if riskIndex > 7
                status = '高风险';
                color = [0.8 0.2 0.2];
            elseif riskIndex > 4
                status = '中风险';
                color = [1 0.6 0];
            else
                status = '低风险';
                color = [0.2 0.7 0.2];
            end

            app.WarningStatusLabel.Text = sprintf('风险评估: %s (指数: %.1f, 预计寿命: %.0f%%)', status, riskIndex, predictedLife);
            app.WarningStatusLabel.FontColor = color;
            app.StatusLabel.Text = '联动预警分析完成';
        end

        % Button pushed function: GenerateReportButton
        function GenerateReportButtonPushed(app, event)
            % Generate comprehensive analysis report
            dataSource = app.DataSourceDropDown.Value;
            analysisType = app.AnalysisTypeDropDown.Value;

            % Create network graph for system relationships
            s = [1 1 2 2 3 3 4 4 5];
            t = [2 3 4 5 6 7 8 9 6];
            weights = [0.8 0.6 0.9 0.7 0.5 0.8 0.6 0.4 0.7];

            G = graph(s, t, weights);
            h = plot(app.AnalysisAxes, G, 'Layout', 'force', 'NodeColor', [0.3 0.6 0.9], ...
                'EdgeColor', [0.5 0.5 0.5], 'LineWidth', 2, 'MarkerSize', 8);
            app.AnalysisAxes.Title.String = sprintf('%s - %s', dataSource, analysisType);

            % Generate report text
            reportText = sprintf(['=== 轮胎状态综合分析报告 ===\n\n' ...
                '数据源: %s\n' ...
                '分析类型: %s\n' ...
                '生成时间: %s\n\n' ...
                '=== 主要发现 ===\n' ...
                '1. 轮胎磨损状态: 整体磨损程度适中，建议定期检查\n' ...
                '2. 胎压监测结果: 胎压波动在正常范围内\n' ...
                '3. 风险评估: 当前风险等级为中等，需要持续监控\n' ...
                '4. 预测分析: 基于当前数据，预计轮胎剩余寿命为70-80%%\n\n' ...
                '=== 建议措施 ===\n' ...
                '• 每月进行一次全面检查\n' ...
                '• 保持适当的胎压水平\n' ...
                '• 注意行驶习惯对轮胎磨损的影响\n' ...
                '• 及时更换磨损严重的轮胎'], ...
                dataSource, analysisType, datestr(now));

            app.ReportTextArea.Value = reportText;
            app.StatusLabel.Text = '数据分析报告已生成';
        end

        % Show specific page
        function showPage(app, pageNum)
            % Hide all pages first
            if ~isempty(app.Page1Panel) && isvalid(app.Page1Panel)
                app.Page1Panel.Visible = 'off';
            end
            if ~isempty(app.Page2Panel) && isvalid(app.Page2Panel)
                app.Page2Panel.Visible = 'off';
            end
            if ~isempty(app.Page3Panel) && isvalid(app.Page3Panel)
                app.Page3Panel.Visible = 'off';
            end
            if ~isempty(app.Page4Panel) && isvalid(app.Page4Panel)
                app.Page4Panel.Visible = 'off';
            end

            % Reset button colors
            app.Tab1Button.BackgroundColor = [0.25 0.4 0.6];
            app.Tab2Button.BackgroundColor = [0.25 0.4 0.6];
            app.Tab3Button.BackgroundColor = [0.25 0.4 0.6];
            app.Tab4Button.BackgroundColor = [0.25 0.4 0.6];

            % Create and show selected page
            switch pageNum
                case 1
                    createPage1(app);
                    app.Page1Panel.Visible = 'on';
                    app.Tab1Button.BackgroundColor = [0.3 0.5 0.7];
                case 2
                    createPage2(app);
                    app.Page2Panel.Visible = 'on';
                    app.Tab2Button.BackgroundColor = [0.3 0.5 0.7];
                case 3
                    createPage3(app);
                    app.Page3Panel.Visible = 'on';
                    app.Tab3Button.BackgroundColor = [0.3 0.5 0.7];
                case 4
                    createPage4(app);
                    app.Page4Panel.Visible = 'on';
                    app.Tab4Button.BackgroundColor = [0.3 0.5 0.7];
            end

            app.CurrentPage = pageNum;
        end

        % Create Page 1: Tire Wear Recognition
        function createPage1(app)
            if isempty(app.Page1Panel) || ~isvalid(app.Page1Panel)
                app.Page1Panel = uipanel(app.MainGrid);
                app.Page1Panel.Title = '';
                app.Page1Panel.BackgroundColor = [1 1 1];
                app.Page1Panel.Layout.Row = 1;
                app.Page1Panel.Layout.Column = 1;

                app.Page1Grid = uigridlayout(app.Page1Panel);
                app.Page1Grid.ColumnWidth = {150, 200, '1x'};
                app.Page1Grid.RowHeight = {40, 40, 40, 40, 40, '1x', 40};
                app.Page1Grid.Padding = [20 20 20 20];
                app.Page1Grid.RowSpacing = 15;
                app.Page1Grid.ColumnSpacing = 20;

                % Wear depth input
                app.WearDepthLabel = uilabel(app.Page1Grid);
                app.WearDepthLabel.Text = '磨损深度 (mm):';
                app.WearDepthLabel.FontWeight = 'bold';
                app.WearDepthLabel.Layout.Row = 1;
                app.WearDepthLabel.Layout.Column = 1;

                app.WearDepthField = uieditfield(app.Page1Grid, 'numeric');
                app.WearDepthField.Value = 2.5;
                app.WearDepthField.Limits = [0 10];
                app.WearDepthField.Layout.Row = 1;
                app.WearDepthField.Layout.Column = 2;

                % Wear area input
                app.WearAreaLabel = uilabel(app.Page1Grid);
                app.WearAreaLabel.Text = '磨损面积 (%):';
                app.WearAreaLabel.FontWeight = 'bold';
                app.WearAreaLabel.Layout.Row = 2;
                app.WearAreaLabel.Layout.Column = 1;

                app.WearAreaField = uieditfield(app.Page1Grid, 'numeric');
                app.WearAreaField.Value = 15.0;
                app.WearAreaField.Limits = [0 100];
                app.WearAreaField.Layout.Row = 2;
                app.WearAreaField.Layout.Column = 2;

                % Wear pattern dropdown
                app.WearPatternLabel = uilabel(app.Page1Grid);
                app.WearPatternLabel.Text = '磨损模式:';
                app.WearPatternLabel.FontWeight = 'bold';
                app.WearPatternLabel.Layout.Row = 3;
                app.WearPatternLabel.Layout.Column = 1;

                app.WearPatternDropDown = uidropdown(app.Page1Grid);
                app.WearPatternDropDown.Items = {'均匀磨损', '偏磨', '中心磨损', '边缘磨损', '斑点磨损'};
                app.WearPatternDropDown.Value = '均匀磨损';
                app.WearPatternDropDown.Layout.Row = 3;
                app.WearPatternDropDown.Layout.Column = 2;

                % Analyze button
                app.AnalyzeWearButton = uibutton(app.Page1Grid, 'push');
                app.AnalyzeWearButton.ButtonPushedFcn = createCallbackFcn(app, @AnalyzeWearButtonPushed, true);
                app.AnalyzeWearButton.Text = '开始分析';
                app.AnalyzeWearButton.BackgroundColor = [0.2 0.6 0.8];
                app.AnalyzeWearButton.FontColor = [1 1 1];
                app.AnalyzeWearButton.FontWeight = 'bold';
                app.AnalyzeWearButton.Layout.Row = 4;
                app.AnalyzeWearButton.Layout.Column = [1 2];

                % Results axes
                app.WearResultAxes = uiaxes(app.Page1Grid);
                app.WearResultAxes.Layout.Row = [1 6];
                app.WearResultAxes.Layout.Column = 3;
                app.WearResultAxes.Title.String = '轮胎磨损可视化';

                % Status label
                app.WearStatusLabel = uilabel(app.Page1Grid);
                app.WearStatusLabel.Text = '等待分析...';
                app.WearStatusLabel.FontSize = 12;
                app.WearStatusLabel.Layout.Row = 7;
                app.WearStatusLabel.Layout.Column = [1 3];
            end
        end
    end

    % Component initialization
    methods (Access = private)

        % Create UIFigure and components
        function createComponents(app)

            % Create UIFigure and hide until all components are created
            app.UIFigure = uifigure('Visible', 'off');
            app.UIFigure.Position = [100 100 1200 800];
            app.UIFigure.Name = '轮胎磨损状态识别与胎压异常联动预警平台';
            app.UIFigure.Resize = 'on';

            % Create GridLayout
            app.GridLayout = uigridlayout(app.UIFigure);
            app.GridLayout.ColumnWidth = {200, '1x'};
            app.GridLayout.RowHeight = {'1x', 30};

            % Create NavigationPanel
            app.NavigationPanel = uipanel(app.GridLayout);
            app.NavigationPanel.Title = '';
            app.NavigationPanel.BackgroundColor = [0.2 0.3 0.4];
            app.NavigationPanel.Layout.Row = 1;
            app.NavigationPanel.Layout.Column = 1;

            % Create NavigationGrid
            app.NavigationGrid = uigridlayout(app.NavigationPanel);
            app.NavigationGrid.ColumnWidth = {'1x'};
            app.NavigationGrid.RowHeight = {60, 50, 50, 50, 50, '1x'};
            app.NavigationGrid.Padding = [10 10 10 10];
            app.NavigationGrid.RowSpacing = 15;

            % Create TitleLabel
            app.TitleLabel = uilabel(app.NavigationGrid);
            app.TitleLabel.FontSize = 14;
            app.TitleLabel.FontWeight = 'bold';
            app.TitleLabel.FontColor = [1 1 1];
            app.TitleLabel.Text = '轮胎预警平台';
            app.TitleLabel.HorizontalAlignment = 'center';
            app.TitleLabel.Layout.Row = 1;
            app.TitleLabel.Layout.Column = 1;

            % Create Tab1Button
            app.Tab1Button = uibutton(app.NavigationGrid, 'push');
            app.Tab1Button.ButtonPushedFcn = createCallbackFcn(app, @Tab1ButtonPushed, true);
            app.Tab1Button.BackgroundColor = [0.3 0.5 0.7];
            app.Tab1Button.FontColor = [1 1 1];
            app.Tab1Button.Text = '磨损识别';
            app.Tab1Button.Layout.Row = 2;
            app.Tab1Button.Layout.Column = 1;

            % Create Tab2Button
            app.Tab2Button = uibutton(app.NavigationGrid, 'push');
            app.Tab2Button.ButtonPushedFcn = createCallbackFcn(app, @Tab2ButtonPushed, true);
            app.Tab2Button.BackgroundColor = [0.25 0.4 0.6];
            app.Tab2Button.FontColor = [1 1 1];
            app.Tab2Button.Text = '胎压监测';
            app.Tab2Button.Layout.Row = 3;
            app.Tab2Button.Layout.Column = 1;

            % Create Tab3Button
            app.Tab3Button = uibutton(app.NavigationGrid, 'push');
            app.Tab3Button.ButtonPushedFcn = createCallbackFcn(app, @Tab3ButtonPushed, true);
            app.Tab3Button.BackgroundColor = [0.25 0.4 0.6];
            app.Tab3Button.FontColor = [1 1 1];
            app.Tab3Button.Text = '联动预警';
            app.Tab3Button.Layout.Row = 4;
            app.Tab3Button.Layout.Column = 1;

            % Create Tab4Button
            app.Tab4Button = uibutton(app.NavigationGrid, 'push');
            app.Tab4Button.ButtonPushedFcn = createCallbackFcn(app, @Tab4ButtonPushed, true);
            app.Tab4Button.BackgroundColor = [0.25 0.4 0.6];
            app.Tab4Button.FontColor = [1 1 1];
            app.Tab4Button.Text = '数据分析';
            app.Tab4Button.Layout.Row = 5;
            app.Tab4Button.Layout.Column = 1;

            % Create MainPanel
            app.MainPanel = uipanel(app.GridLayout);
            app.MainPanel.Title = '';
            app.MainPanel.BackgroundColor = [1 1 1];
            app.MainPanel.Layout.Row = 1;
            app.MainPanel.Layout.Column = 2;

            % Create MainGrid
            app.MainGrid = uigridlayout(app.MainPanel);
            app.MainGrid.ColumnWidth = {'1x'};
            app.MainGrid.RowHeight = {'1x'};
            app.MainGrid.Padding = [0 0 0 0];

            % Create StatusPanel
            app.StatusPanel = uipanel(app.GridLayout);
            app.StatusPanel.Title = '';
            app.StatusPanel.BackgroundColor = [0.9 0.9 0.9];
            app.StatusPanel.Layout.Row = 2;
            app.StatusPanel.Layout.Column = [1 2];

            % Create StatusLabel
            app.StatusLabel = uilabel(app.StatusPanel);
            app.StatusLabel.Text = '系统就绪';
            app.StatusLabel.Position = [10 5 1180 20];
            app.StatusLabel.FontSize = 11;

            % Show the figure after all components are created
            app.UIFigure.Visible = 'on';
        end

        % Create Page 2: Pressure Monitoring
        function createPage2(app)
            if isempty(app.Page2Panel) || ~isvalid(app.Page2Panel)
                app.Page2Panel = uipanel(app.MainGrid);
                app.Page2Panel.Title = '';
                app.Page2Panel.BackgroundColor = [1 1 1];
                app.Page2Panel.Layout.Row = 1;
                app.Page2Panel.Layout.Column = 1;

                app.Page2Grid = uigridlayout(app.Page2Panel);
                app.Page2Grid.ColumnWidth = {150, 200, '1x'};
                app.Page2Grid.RowHeight = {40, 40, 40, 40, 40, '1x', 40};
                app.Page2Grid.Padding = [20 20 20 20];
                app.Page2Grid.RowSpacing = 15;
                app.Page2Grid.ColumnSpacing = 20;

                % Pressure input
                app.PressureLabel = uilabel(app.Page2Grid);
                app.PressureLabel.Text = '胎压 (bar):';
                app.PressureLabel.FontWeight = 'bold';
                app.PressureLabel.Layout.Row = 1;
                app.PressureLabel.Layout.Column = 1;

                app.PressureField = uieditfield(app.Page2Grid, 'numeric');
                app.PressureField.Value = 2.4;
                app.PressureField.Limits = [0 5];
                app.PressureField.Layout.Row = 1;
                app.PressureField.Layout.Column = 2;

                % Temperature input
                app.TemperatureLabel = uilabel(app.Page2Grid);
                app.TemperatureLabel.Text = '温度 (°C):';
                app.TemperatureLabel.FontWeight = 'bold';
                app.TemperatureLabel.Layout.Row = 2;
                app.TemperatureLabel.Layout.Column = 1;

                app.TemperatureField = uieditfield(app.Page2Grid, 'numeric');
                app.TemperatureField.Value = 25.0;
                app.TemperatureField.Limits = [-20 80];
                app.TemperatureField.Layout.Row = 2;
                app.TemperatureField.Layout.Column = 2;

                % Load input
                app.LoadLabel = uilabel(app.Page2Grid);
                app.LoadLabel.Text = '载荷 (kg):';
                app.LoadLabel.FontWeight = 'bold';
                app.LoadLabel.Layout.Row = 3;
                app.LoadLabel.Layout.Column = 1;

                app.LoadField = uieditfield(app.Page2Grid, 'numeric');
                app.LoadField.Value = 500;
                app.LoadField.Limits = [0 2000];
                app.LoadField.Layout.Row = 3;
                app.LoadField.Layout.Column = 2;

                % Monitor button
                app.MonitorButton = uibutton(app.Page2Grid, 'push');
                app.MonitorButton.ButtonPushedFcn = createCallbackFcn(app, @MonitorButtonPushed, true);
                app.MonitorButton.Text = '开始监测';
                app.MonitorButton.BackgroundColor = [0.2 0.7 0.3];
                app.MonitorButton.FontColor = [1 1 1];
                app.MonitorButton.FontWeight = 'bold';
                app.MonitorButton.Layout.Row = 4;
                app.MonitorButton.Layout.Column = [1 2];

                % Results axes
                app.PressureAxes = uiaxes(app.Page2Grid);
                app.PressureAxes.Layout.Row = [1 6];
                app.PressureAxes.Layout.Column = 3;
                app.PressureAxes.Title.String = '胎压监测分析';

                % Status label
                app.PressureStatusLabel = uilabel(app.Page2Grid);
                app.PressureStatusLabel.Text = '等待监测...';
                app.PressureStatusLabel.FontSize = 12;
                app.PressureStatusLabel.Layout.Row = 7;
                app.PressureStatusLabel.Layout.Column = [1 3];
            end
        end

        % Create Page 3: Integrated Warning
        function createPage3(app)
            if isempty(app.Page3Panel) || ~isvalid(app.Page3Panel)
                app.Page3Panel = uipanel(app.MainGrid);
                app.Page3Panel.Title = '';
                app.Page3Panel.BackgroundColor = [1 1 1];
                app.Page3Panel.Layout.Row = 1;
                app.Page3Panel.Layout.Column = 1;

                app.Page3Grid = uigridlayout(app.Page3Panel);
                app.Page3Grid.ColumnWidth = {150, 200, '1x'};
                app.Page3Grid.RowHeight = {40, 40, 40, 40, 40, '1x', 40};
                app.Page3Grid.Padding = [20 20 20 20];
                app.Page3Grid.RowSpacing = 15;
                app.Page3Grid.ColumnSpacing = 20;

                % Threshold input
                app.ThresholdLabel = uilabel(app.Page3Grid);
                app.ThresholdLabel.Text = '预警阈值:';
                app.ThresholdLabel.FontWeight = 'bold';
                app.ThresholdLabel.Layout.Row = 1;
                app.ThresholdLabel.Layout.Column = 1;

                app.ThresholdField = uieditfield(app.Page3Grid, 'numeric');
                app.ThresholdField.Value = 0.75;
                app.ThresholdField.Limits = [0 1];
                app.ThresholdField.Layout.Row = 1;
                app.ThresholdField.Layout.Column = 2;

                % Risk index input
                app.RiskIndexLabel = uilabel(app.Page3Grid);
                app.RiskIndexLabel.Text = '风险指数:';
                app.RiskIndexLabel.FontWeight = 'bold';
                app.RiskIndexLabel.Layout.Row = 2;
                app.RiskIndexLabel.Layout.Column = 1;

                app.RiskIndexField = uieditfield(app.Page3Grid, 'numeric');
                app.RiskIndexField.Value = 5.0;
                app.RiskIndexField.Limits = [0 10];
                app.RiskIndexField.Layout.Row = 2;
                app.RiskIndexField.Layout.Column = 2;

                % Predict button
                app.PredictLifeButton = uibutton(app.Page3Grid, 'push');
                app.PredictLifeButton.ButtonPushedFcn = createCallbackFcn(app, @PredictLifeButtonPushed, true);
                app.PredictLifeButton.Text = '风险评估';
                app.PredictLifeButton.BackgroundColor = [0.8 0.4 0.2];
                app.PredictLifeButton.FontColor = [1 1 1];
                app.PredictLifeButton.FontWeight = 'bold';
                app.PredictLifeButton.Layout.Row = 3;
                app.PredictLifeButton.Layout.Column = [1 2];

                % Results axes
                app.WarningAxes = uiaxes(app.Page3Grid);
                app.WarningAxes.Layout.Row = [1 6];
                app.WarningAxes.Layout.Column = 3;
                app.WarningAxes.Title.String = '联动预警分析';

                % Status label
                app.WarningStatusLabel = uilabel(app.Page3Grid);
                app.WarningStatusLabel.Text = '等待评估...';
                app.WarningStatusLabel.FontSize = 12;
                app.WarningStatusLabel.Layout.Row = 7;
                app.WarningStatusLabel.Layout.Column = [1 3];
            end
        end

        % Create Page 4: Data Analysis
        function createPage4(app)
            if isempty(app.Page4Panel) || ~isvalid(app.Page4Panel)
                app.Page4Panel = uipanel(app.MainGrid);
                app.Page4Panel.Title = '';
                app.Page4Panel.BackgroundColor = [1 1 1];
                app.Page4Panel.Layout.Row = 1;
                app.Page4Panel.Layout.Column = 1;

                app.Page4Grid = uigridlayout(app.Page4Panel);
                app.Page4Grid.ColumnWidth = {150, 200, '1x'};
                app.Page4Grid.RowHeight = {40, 40, 40, 40, '1x', 200};
                app.Page4Grid.Padding = [20 20 20 20];
                app.Page4Grid.RowSpacing = 15;
                app.Page4Grid.ColumnSpacing = 20;

                % Data source dropdown
                app.DataSourceLabel = uilabel(app.Page4Grid);
                app.DataSourceLabel.Text = '数据源:';
                app.DataSourceLabel.FontWeight = 'bold';
                app.DataSourceLabel.Layout.Row = 1;
                app.DataSourceLabel.Layout.Column = 1;

                app.DataSourceDropDown = uidropdown(app.Page4Grid);
                app.DataSourceDropDown.Items = {'实时数据', '历史数据', '模拟数据'};
                app.DataSourceDropDown.Value = '实时数据';
                app.DataSourceDropDown.Layout.Row = 1;
                app.DataSourceDropDown.Layout.Column = 2;

                % Analysis type dropdown
                app.AnalysisTypeLabel = uilabel(app.Page4Grid);
                app.AnalysisTypeLabel.Text = '分析类型:';
                app.AnalysisTypeLabel.FontWeight = 'bold';
                app.AnalysisTypeLabel.Layout.Row = 2;
                app.AnalysisTypeLabel.Layout.Column = 1;

                app.AnalysisTypeDropDown = uidropdown(app.Page4Grid);
                app.AnalysisTypeDropDown.Items = {'趋势分析', '相关性分析', '预测分析', '异常检测'};
                app.AnalysisTypeDropDown.Value = '趋势分析';
                app.AnalysisTypeDropDown.Layout.Row = 2;
                app.AnalysisTypeDropDown.Layout.Column = 2;

                % Generate report button
                app.GenerateReportButton = uibutton(app.Page4Grid, 'push');
                app.GenerateReportButton.ButtonPushedFcn = createCallbackFcn(app, @GenerateReportButtonPushed, true);
                app.GenerateReportButton.Text = '生成报告';
                app.GenerateReportButton.BackgroundColor = [0.5 0.2 0.8];
                app.GenerateReportButton.FontColor = [1 1 1];
                app.GenerateReportButton.FontWeight = 'bold';
                app.GenerateReportButton.Layout.Row = 3;
                app.GenerateReportButton.Layout.Column = [1 2];

                % Analysis axes
                app.AnalysisAxes = uiaxes(app.Page4Grid);
                app.AnalysisAxes.Layout.Row = [1 5];
                app.AnalysisAxes.Layout.Column = 3;
                app.AnalysisAxes.Title.String = '数据分析可视化';

                % Report text area
                app.ReportTextArea = uitextarea(app.Page4Grid);
                app.ReportTextArea.Layout.Row = 6;
                app.ReportTextArea.Layout.Column = [1 3];
                app.ReportTextArea.Value = '分析报告将在此显示...';
            end
        end
    end

    % App creation and deletion
    methods (Access = public)

        % Construct app
        function app = TireWearPressureWarningPlatform

            % Create UIFigure and components
            createComponents(app)

            % Register the app with App Designer
            registerApp(app, app.UIFigure)

            % Execute the startup function
            runStartupFcn(app, @startupFcn)

            if nargout == 0
                clear app
            end
        end

        % Code that executes before app deletion
        function delete(app)

            % Delete UIFigure when app is deleted
            delete(app.UIFigure)
        end
    end
end
