function runTireWarningApp()
% 启动轮胎磨损状态识别与胎压异常联动预警平台
%
% 使用方法:
%   runTireWarningApp()
%
% 功能说明:
%   这个函数启动轮胎磨损状态识别与胎压异常联动预警平台的主界面
%   该平台包含以下四个主要功能模块：
%   1. 磨损识别 - 分析轮胎磨损状态和模式
%   2. 胎压监测 - 实时监测胎压、温度和载荷
%   3. 联动预警 - 综合风险评估和预警
%   4. 数据分析 - 生成综合分析报告
%
% 系统要求:
%   - MATLAB R2023b 或更高版本
%   - 支持App Designer的MATLAB环境
%
% 作者: AI Assistant
% 日期: 2025年

try
    % 检查MATLAB版本
    if verLessThan('matlab', '9.15') % R2023b
        warning('建议使用MATLAB R2023b或更高版本以获得最佳体验');
    end

    % 启动应用程序
    fprintf('正在启动轮胎磨损状态识别与胎压异常联动预警平台...\n');

    % 创建并显示应用程序
    app = TireWearPressureWarningPlatform;

    fprintf('平台启动成功！\n');
    fprintf('功能模块说明：\n');
    fprintf('  - 磨损识别：输入磨损参数进行轮胎状态分析\n');
    fprintf('  - 胎压监测：监测胎压、温度和载荷数据\n');
    fprintf('  - 联动预警：综合评估风险并提供预警\n');
    fprintf('  - 数据分析：生成详细的分析报告\n');

catch ME
    fprintf('启动失败：%s\n', ME.message);
    if ~isempty(ME.stack)
        fprintf('错误位置：%s (行 %d)\n', ME.stack(1).name, ME.stack(1).line);
    end
    fprintf('请确保：\n');
    fprintf('1. 使用MATLAB R2023b或更高版本\n');
    fprintf('2. TireWearPressureWarningPlatform.m文件在当前路径中\n');
    fprintf('3. 具有App Designer的完整MATLAB安装\n');
end

end
