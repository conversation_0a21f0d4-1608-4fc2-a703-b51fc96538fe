# 轮胎磨损状态识别与胎压异常联动预警平台 - 使用指南

## 🚀 快速开始

### 启动应用程序
在MATLAB命令窗口中输入：
```matlab
runTireWarningApp
```

或者直接创建应用实例：
```matlab
app = TireWearPressureWarningPlatform;
```

## 📋 功能模块详解

### 1. 磨损识别模块 🔍

**功能说明**：分析轮胎磨损状态，识别磨损模式，评估轮胎健康状况。

**操作步骤**：
1. 点击左侧导航栏的"磨损识别"按钮
2. 输入以下参数：
   - **磨损深度 (mm)**：轮胎花纹沟槽的磨损深度 (0-10mm)
   - **磨损面积 (%)**：磨损区域占轮胎总面积的百分比 (0-100%)
   - **磨损模式**：从下拉菜单选择磨损类型
3. 点击"开始分析"按钮
4. 查看右侧的极坐标可视化结果

**磨损模式说明**：
- **均匀磨损**：正常的均匀磨损模式
- **偏磨**：轮胎一侧磨损严重
- **中心磨损**：轮胎中央部分磨损
- **边缘磨损**：轮胎边缘磨损严重
- **斑点磨损**：不规则的斑点状磨损

**结果解读**：
- 🟢 轻微磨损：磨损深度 < 1.6mm
- 🟡 中度磨损：磨损深度 1.6-3.0mm
- 🔴 严重磨损：磨损深度 > 3.0mm

### 2. 胎压监测模块 📊

**功能说明**：实时监测胎压、温度和载荷，分析胎压稳定性。

**操作步骤**：
1. 点击左侧导航栏的"胎压监测"按钮
2. 输入监测参数：
   - **胎压 (bar)**：当前胎压值 (0-5 bar)
   - **温度 (°C)**：环境或轮胎温度 (-20°C到80°C)
   - **载荷 (kg)**：车辆当前载重 (0-2000kg)
3. 点击"开始监测"按钮
4. 查看3D表面图显示的胎压-温度关系

**正常胎压范围**：
- 🟢 正常：2.0-2.8 bar
- 🔴 异常：< 2.0 bar 或 > 2.8 bar

### 3. 联动预警模块 ⚠️

**功能说明**：综合磨损和胎压数据，进行风险评估和寿命预测。

**操作步骤**：
1. 点击左侧导航栏的"联动预警"按钮
2. 设置预警参数：
   - **预警阈值**：预警敏感度设置 (0-1)
   - **风险指数**：当前风险评分 (0-10)
3. 点击"风险评估"按钮
4. 查看风险评估热力图和预测结果

**风险等级**：
- 🟢 低风险：风险指数 < 4
- 🟡 中风险：风险指数 4-7
- 🔴 高风险：风险指数 > 7

### 4. 数据分析模块 📈

**功能说明**：生成综合分析报告，提供专业的数据分析和建议。

**操作步骤**：
1. 点击左侧导航栏的"数据分析"按钮
2. 选择分析配置：
   - **数据源**：实时数据/历史数据/模拟数据
   - **分析类型**：趋势分析/相关性分析/预测分析/异常检测
3. 点击"生成报告"按钮
4. 查看网络图可视化和详细报告

## 🎯 使用技巧

### 最佳实践
1. **定期监测**：建议每月进行一次全面检查
2. **参数记录**：记录历史数据以便趋势分析
3. **及时响应**：发现异常时及时采取措施
4. **综合分析**：结合多个模块的结果进行综合判断

### 参数建议值
- **新轮胎磨损深度**：8-10mm
- **更换阈值**：磨损深度 < 1.6mm
- **标准胎压**：2.2-2.5 bar
- **正常温度范围**：15-35°C

## 🔧 故障排除

### 常见问题解决

**Q: 应用程序无法启动**
A: 
- 检查MATLAB版本是否为R2023b或更高
- 确认所有文件在同一目录中
- 重启MATLAB后重试

**Q: 图表显示异常**
A:
- 检查输入参数是否在有效范围内
- 尝试调整窗口大小
- 重新执行分析

**Q: 分析结果不准确**
A:
- 确认输入参数的准确性
- 检查测量设备的校准
- 考虑环境因素的影响

## 📞 技术支持

如遇到技术问题，请检查：
1. MATLAB版本兼容性
2. 输入参数的有效性
3. 系统资源使用情况

---

**版本信息**：v1.0.0  
**更新日期**：2025年7月31日  
**兼容性**：MATLAB R2023b+
