% 简单测试脚本
try
    fprintf('开始测试应用程序...\n');
    
    % 检查文件是否存在
    if exist('TireWearPressureWarningPlatform.m', 'file')
        fprintf('找到主应用程序文件\n');
    else
        fprintf('错误：找不到主应用程序文件\n');
        return;
    end
    
    % 尝试创建应用程序
    fprintf('正在创建应用程序实例...\n');
    app = TireWearPressureWarningPlatform;
    
    fprintf('应用程序创建成功！\n');
    fprintf('应用程序窗口应该已经显示\n');
    
catch ME
    fprintf('测试失败：%s\n', ME.message);
    fprintf('错误位置：%s (行 %d)\n', ME.stack(1).file, ME.stack(1).line);
end
